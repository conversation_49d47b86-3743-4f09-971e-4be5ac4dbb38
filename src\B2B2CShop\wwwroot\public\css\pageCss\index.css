 /* 首页- 主体 --------------- 开始 ------------ -----------------------------------------------------*/

 @media screen and (min-width: 1552px) {
     .mainBox1_container {
         margin-left: 0% !important;
         width: 100% !important;
     }

     .mainBox1 {
         width: 1552px !important;
         margin: 20px auto;

         margin-left: calc((100% - 1552px) / 2);
     }

     .mainBox2 {
         margin: 80px auto 20px;
         width: 1552px;
         padding: 1vw 0px;
         display: flex;
     }

     .rangeBox {
         margin: 20px auto;
         width: 1552px;
         padding: 1vw 0px;
         background-color: white;
     }

     .guessLikeBox {
         margin: 20px auto;
         width: 1552px;
         padding: 1vw 0px;
         background-color: white;
     }

     .providerBox {
         margin: 20px auto;
         width: 1552px;
         padding: 1vw 0px;
         background-color: white;
     }

     .mainBox0>div:not(.mainBox1_container) {
         width: 1552px !important;
     }


     .mainBox1_center_left {
         /* width: 68% !important; */
         width: 44vw;
     }

     .mainBox1_center_right {
         width: 30% !important;
     }

     .mainBox1_center>div:nth-child(1) {
         height: calc(100% - 100px) !important;
     }

     .mainBox1_bottom {
         margin-top: 10px;
         max-height: 100px !important;
     }

     .header2_key {
         font-size: 13px !important;
     }

 }

 @media screen and (max-width: 1552px) {
     .mainBox1 {
         width: 1200px !important;
         margin: 20px auto;
         margin-left: calc((100% - 1200px) / 2);
     }

     .mainBox0>div:not(.mainBox1_container) {
         width: 1200px !important;

     }

     .mainBox2 {
         margin: 80px auto 20px;
         width: 1200px;
         padding: 1vw 0px;
         display: flex;
     }

     .rangeBox {
         margin: 20px auto;
         width: 1200px;
         padding: 1vw 0px;
         background-color: white;
     }

     .guessLikeBox {
         margin: 20px auto;
         width: 1200px;
         padding: 1vw 0px;
         background-color: white;
     }

     .providerBox {
         margin: 20px auto;
         width: 1200px;
         padding: 1vw 0px;
         background-color: white;
     }
 }

 .mainBox0 {
     width: 100%;
     background-color: #EFF1F5;
     overflow-x: hidden;
     padding-bottom: 50px;
 }

 .mainBox1_container {
     position: relative;
     width: 100vw;
     min-width: 1300px;
     height: 600px;
     background: var(--bg-mainBox1);
     padding-top: 30px;
     /* 临时更改 */
     height: 400px;
 }

 .mainBox1 {
     position: absolute;
     padding: 40px 0px;
     height: 560px;
     min-height: 400px;
     max-height: 600px;
     background-color: white;
     display: flex;

     /* 临时更改 */
     padding: 10px 0;
     height: 36px;
     /* border: 1px solid #000; */
 }

 .mainBox1_left {
     width: 20%;
     position: relative;
     /* overflow: auto; */
 }

 .onePage {
     position: absolute;
     width: 100%;
     height: 100%;
     /* max-height: 30vw; */
     background-color: white;
     overflow-y: scroll;
     z-index: 2;
 }

 .onePage::-webkit-scrollbar {
     width: 10px;
 }

 /* 二段头部 */
 .header2 {
     background-color: #f7f7f791 !important;
     color: var(--text-color);
 }

 /* 取消掉产品分类select的默认样式 */
 .header2>.headerForm>.layui-form-select>.layui-select-title>.layui-input {
     color: var(--text-color);
 }

 .header2_key {
     color: var(--text-color);
 }

 /* header2 */
 .header2_key:hover {
     color: dodgerblue;

 }

 .footer {
     background-color: #445268;
 }

 .footer2>div>div:nth-child(1) {
     color: white;
 }

 .productCategory[data-select="true"] {
     color: var(--blue-deep);
     border-bottom: 1px solid;
 }



 .onePage>div {
     padding: 15px 0px;
     display: flex;
     color: var(--text-color);
     cursor: pointer;
     white-space: nowrap;
     text-align: left;
     font-size: 1em;
     text-indent: 1.7cqw;
 }

 .hoverPages {
     position: relative;
     left: 100%;
     width: 396%;
     height: 100%;
     max-height: 40vw;
     background-color: white;
     overflow: hidden;
     display: none;
     /* display: block; */
     z-index: 2;
     border-radius: 0px 10px 10px 0px;
     border: 1px solid var(--line);
 }



 .hoverPages>div>.category-item:hover {
     color: var(--blue-deep);
 }

 .hoverPages:hover {
     display: flex;
 }

 .hoverPages>div>div {
     display: flex;
     color: var(--text-color);
     cursor: pointer;
     white-space: nowrap;
     text-align: left;
     font-size: 14px;
     text-indent: 10px;
 }

 .twoPage {
     flex: 3;
     overflow-y: scroll;
 }

 .twoPage>div[data-select="true"] {
     color: var(--blue-deep);
     /* background-color: var(--text-color4); */
 }

 .onePage>div[data-select="true"] {
     color: var(--blue-deep);
     /* background-color: var(--text-color4); */
 }

 /* 美化滚动条 */
 .onePage::-webkit-scrollbar {
     width: .3vw;
 }

 .onePage::-webkit-scrollbar-track {
     width: 6px;
     background: rgba(#101F1C, 0.1);
     -webkit-border-radius: 2em;
     -moz-border-radius: 2em;
     border-radius: 2em;
 }

 .onePage::-webkit-scrollbar-thumb {
     background-color: #f2f2f2;
     background-clip: padding-box;
     min-height: 28px;
     -webkit-border-radius: 2em;
     -moz-border-radius: 2em;
     border-radius: 2em;
     transition: background-color .3s;
     cursor: pointer;
 }

 .onePage::-webkit-scrollbar-thumb:hover {
     background-color: rgba(144, 147, 153, .3);
 }



 .threePageMore {
     position: absolute;
     top: 100px;
     right: 10vw;
     font-size: .9vw !important;
 }

 .rightIcon {
     font-size: 20px;
     margin-left: auto;
     margin-right: 1.0em;
 }

 .rightIcon2 {
     font-size: 20px;
     text-indent: 6px;
 }

 .mainBox1_center {
     display: flex;
     flex-direction: column;
     width: 80%;
     height: 100%;
     /* 临时更改 */
     height: 500px;

 }

 .mainBox1_center>div:nth-child(1) {
     width: 100%;
     height: calc(100% - 25%);
     display: flex;
 }

 .mainBox1_bottom {
     width: 100%;
     height: 25%;
     display: flex;
 }

 .mainBox1_bottom>div {
     flex: 1;
     border: 1px solid var(--line);
     margin: 10px;
     border-radius: 5px;
     color: var(--text-color);
 }

 .mainBox1_center_left {
     width: 50vw;
     min-width: 650px;
     height: 100%;
     overflow: hidden;
 }

 .mainBox1_center_right {
     width: 25%;
     min-width: 300px;
     height: 100%;
     overflow: hidden;
 }

 .mainBox1_center_right>div:nth-child(1) {
     width: 100%;
     height: 27%;
     border-bottom: 1px solid var(--line);
     text-align: center;
 }

 .mainBox1_center_right>div:nth-child(2) {
     width: 100%;
     height: 40%;
     border-bottom: 1px solid var(--line);
 }

 .mainBox1_center_right>div:nth-child(3) {
     width: 100%;
     height: 33%;
 }

 .loginBtn {
     width: 73px;
     height: 25px;
     line-height: 0px;
     background: #E72A19;
     border-radius: 13px 13px 13px 13px;
     color: white;
     border: none;
     outline: none;
     margin-right: 20px;
 }

 .registerBtn {
     width: 73px;
     height: 25px;
     line-height: 0px;
     background: #2C79E8;
     border-radius: 13px 13px 13px 13px;
     color: white;
     border: none;
     outline: none;
 }

 .NoticeBoxContainer {
     padding-top: .7vw;
     display: flex;
     max-height: 200px;
 }

 .noticeBox {
     margin-top: 10px;
     margin-left: 10%;
     width: 80%;
     height: 70%;
     overflow: hidden;
     color: var(--text-color);
     font-size: 1em;
     /* border: 2px solid red; */
 }

 .noticeBox>div {
     padding: 5px 0px;
     cursor: pointer;
 }

 .advert>a {
     display: block;
     margin-left: 5%;
     width: 90%;
     height: 90%;
 }

 .advert>a>img {
     object-fit: cover;
     height: 90%;
     width: 90%;
     border: 2px dotted rgb(189, 188, 188);
     box-sizing: border-box;
 }

 /* 轮播开始 */
 .swiper-container {
     width: 100%;
     height: 100%;
     position: relative;
 }

 .pagination {
     position: absolute;
     z-index: 20;
     bottom: 10px;
     width: 100%;
     text-align: center;
 }

 .swiper-pagination-switch {
     display: inline-block;
     width: 8px;
     height: 8px;
     border-radius: 8px;
     background: #bbbaba;
     margin: 0 5px;
     opacity: 0.8;
     border: 1px solid #fff;
     cursor: pointer;
 }

 .swiper-active-switch {
     background: dodgerblue;
 }

 .swiper-slide>img {
     width: 100%;
     height: 100%;
     object-fit: cover;
 }

 /* 轮播结束 */

 .userInfoBox2>div {
     margin: 5px auto;
     height: 40%;
     max-width: 80%;
 }

 .userInfoBox2>div:nth-child(2)>div {
     padding: 0vw .1vw .6vw .8vw;
     text-align: center;
     min-width: 62px;
 }

 .userInfoBox2>div:nth-child(2)>div:hover {
     color: var(--blue-deep);
     cursor: pointer;
 }

 /* icon */
 .iconSize28 {
     font-size: 28px;
 }

 /* 文字 */
 .userInfoBox2>div:nth-child(2)>div>div:nth-child(2) {
     font-size: 12px;
     white-space: nowrap;
 }


 /* 猜你喜欢 */
 /* .guessLikeBox {
    margin-top: 20px;
    width: 80%;
    margin-left: 10%;
    padding: 1vw 0px;
    background-color: white;
} */

 .guessLikeBox>div {
     padding-left: 1.1vw;
     font-size: 30px;
 }

 .guessContainer {
     width: calc(100% - 20px);
     display: flex;
     flex-wrap: wrap;
     justify-content: left;
     /* border: 1px solid red !important; */
 }

 .guessContent {
     margin-top: 10px;
     display: flex;
     justify-content: center;
     width: calc(100% / 7.5);
     flex-direction: column;
     background-color: white;
     padding: 1vw 1.4vw;
     border-radius: 10px;
     cursor: pointer;
 }

 .guessContent_desc {
     font-size: 12px;
     font-weight: 380;
     color: #666666;
     display: -webkit-box;
     word-break: break-all;
     -webkit-box-orient: vertical;
     -webkit-line-clamp: 2;
     overflow: hidden;
     text-overflow: ellipsis;
     margin: 10px 0 0;
     height: 28px;
 }

 .guessContent>div:nth-child(3) {
     padding: 7px 0px;
     font-size: 12px;
 }

 .guessContent_price {
     font-size: 20px;
 }



 .mainBox2>div:nth-child(1) {
     background-color: #FFEADF;
     width: 48.5%;
     padding: 0.6vw;
 }

 .mainBox2>div:nth-child(2) {
     margin-left: auto;
     background-color: #FFF4D7;
     width: 48.5%;
     padding: 0.6vw;
 }

 .mainBox2_container {
     padding: 10px;
     display: flex;
     justify-content: flex-start;
     align-items: stretch;
     box-sizing: border-box;
     overflow: hidden;
 }

 .mainBox2_content_home {
     display: flex;
     justify-content: center;
     flex-direction: column;
     background-color: white;
     padding: 10px;
     border-radius: 10px;
     border: 1px solid rgb(230, 230, 230);
     cursor: pointer;
     width: calc((100% - 30px) / 4);
     min-height: 200px;
     box-sizing: border-box;
     overflow: hidden;
 }

 .mainBox2_content_home:not(:last-child) {
     margin-right: 10px;
 }

 .mainBox2_content_home>div:nth-child(3) {
     margin: 7px 0;
 }

 .mainBox2_content_desc {
     font-size: 12px;
     font-weight: 380;
     letter-spacing: 1px;
     color: #666666;

 }

 .mainBox2_content_price {
     font-size: 24px;
 }

 /* 响应式布局 - 一行4个元素 */
 @media screen and (max-width: 1200px) {
     .mainBox2_content_home {
         width: calc((100% - 24px) / 4);
     }

     .mainBox2_content_home:not(:last-child) {
         margin-right: 8px;
     }
 }

 @media screen and (max-width: 768px) {
     .mainBox2_content_home {
         width: calc((100% - 15px) / 4);
     }

     .mainBox2_content_home:not(:last-child) {
         margin-right: 5px;
     }
 }

 .rangeBox>div {
     padding-left: 1.1vw;
     font-size: 30px;
 }

 .rangeFunc>div {
     padding: 5px 10px;
     font-size: 14px;
     border: 1px solid;
     border-radius: 23px;
     color: var(--info-text);
     cursor: pointer;

 }

 .rangeFunc>div:nth-child(n+2) {
     margin-left: 1vw;
 }

 .rangeContainer {
     margin-top: 1vw;
     padding: .5vw .5vw;
     box-sizing: border-box;
     display: flex;
     justify-content: left;
 }

 .rangeContainer>div {
     flex: 1;
     border-radius: 18px;


 }

 .rangeContent {
     margin: 1.5vw 0vw;
     display: flex;
     justify-content: flex-start;
     place-items: center;
     position: relative;
 }

 .rangeContent>div:nth-child(1) {
     margin: 0 0 0 1.3vw;
     width: 130px;
     height: 130px;
     background-color: white;
     border-radius: 10px;
 }

 .rangeContent>div:nth-child(1)>img {
     border-radius: 15px 13px 13px 13px;
     cursor: pointer;
     object-fit: contain;
 }

 .rangeContent>div:nth-child(2) {
     flex: 1;
     word-break: break-all;
     margin: 0 10px;
     font-size: .8vw;
     cursor: pointer;
 }

 .rangeContent1::before {
     content: '01';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #fe374a;
     color: white;
     font-size: 11px;
 }

 .rangeContent2::before {
     content: '02';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #ec8944;
     color: white;
     font-size: 11px;
 }

 .rangeContent3::before {
     content: '03';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #ffd164;
     color: white;
     font-size: 11px;
 }

 .rangeContent4::before {
     content: '04';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     font-size: 11px;
 }

 .rangeContent5::before {
     content: '05';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent6::before {
     content: '06';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent7::before {
     content: '07';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent8::before {
     content: '08';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent9::before {
     content: '09';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent10::before {
     content: '10';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent11::before {
     content: '11';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent12::before {
     content: '12';
     position: absolute;
     top: 0;
     left: 1.3vw;
     width: .6vw;
     height: .8vw;
     padding: 5px;
     background-color: #9C9C9C;
     color: white;
     /* border-radius: 10px 5px 0px 0px; */
     font-size: 11px;
 }

 .rangeContent::after {
     content: '';
     position: absolute;
     left: 1.3vw;
     top: 1vw;
     width: 0;
     height: 0;
     border-left: .6vw solid transparent;
     border-right: .6vw solid transparent;
     border-bottom: 0.4vw solid white;
 }

 /* 供货商品牌开始 */
 /* .providerBox {
    margin-top: 20px;
    width: 80%;
    margin-left: 10%;
    padding: 1vw 0px;
    background-color: white;
} */

 .providerBox>div {
     padding-left: 1.1vw;
     font-size: 30px;
 }

 .providerContainer {
     width: calc(100% - 20px);
     display: flex;
     flex-wrap: wrap;
     justify-content: left;
     /* border: 1px solid red !important; */
 }

 .providerContent {
     margin-top: 20px;
     display: flex;
     justify-content: center;
     place-items: center;
     width: 7%;
     height: 5vw;
     background-color: white;
     margin-right: 1.1vw;
     padding: 0.5vw;
     border-radius: 10px;
     border: 1px solid var(--line);
     cursor: pointer;
 }

 /* 侧边功能栏 */
 .aside {
     width: 3%;
     min-width: 60px;
     background-color: white;
     position: fixed;
     top: 30%;
     right: 0%;
     border-radius: 20px 0px 0px 20px;
     padding: .3vw;
     display: block !important;
     z-index: 9999 !important;

 }

 .asideContainer {
     height: 100%;
     display: flex;
     flex-direction: column;
     user-select: none;
     gap: 10px;
     padding: 10px 0;
 }

 .asideContent {
     display: flex;
     flex-direction: column;
     text-align: center;
     /* line-height: 30px; */
     font-size: .7vw;
     cursor: pointer;
     position: relative;
 }

 .asideContent:hover {
     color: dodgerblue;
 }

 .asideContent>img {
     margin: auto;
     width: 22px;
     height: 20px;
     object-fit: contain;
     margin-bottom: 5px;
 }

 .employeeBox {
     width: 100%;
     background-color: #2C79E8;
     padding: 20px 0;
 }

 .employeeBox>div {
     display: flex;
     justify-content: center;
     text-align: center;
     color: white;
     width: 80%;
     margin-left: 10%;
 }

 .employeeBox>div>div {
     flex: 1;
     width: 20%;
 }

 /* 私有footer css */
 .footer1>table>tbody>tr>td>a:hover {
     color: white;
     cursor: pointer;
 }

 .footer1>table>thead>tr>th>a {
     color: white !important;
     text-align: left !important;
 }

 .sub_wx {
     color: white !important;
 }



 .serviceBox {
     position: absolute;
     top: 100px;
     right: 100%;
     margin-right: 20px;
     transform: translateY(-50%);
     background-color: #fff;
     display: none;
     border: 1px solid #ddd;
     border-radius: 8px;
     padding: 0;
     box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
     white-space: normal;
     z-index: 1000;
     font-size: 12px;
     color: var(--text-color);
     width: 150px;
     box-sizing: border-box;
     text-align: left;
 }

 .serviceBox-header {
     padding: 10px 0;
     margin: 0 15px;
     border-radius: 8px 8px 0 0;
     display: flex;
     align-items: center;
     gap: 8px;
     border-bottom: 1px dashed #D9D9D9;
 }

 .serviceBox-icon {
     width: 20px;
     height: 20px;
     filter: brightness(0);
 }

 .serviceBox-title {
     font-weight: bold;
     color: var(--text-color);
 }

 /* serviceBox 内容样式 */
 .serviceBox-content {
     padding: 15px;
 }

 .serviceBox-item {
     display: flex;
     align-items: center;
     gap: 8px;
     margin-bottom: 10px;
 }

 .serviceBox-item-icon {
     width: 16px;
     height: 16px;
     opacity: 0.7;
 }

 .serviceBox-section {
     padding-top: 10px;

 }

 .serviceBox-section:first-child {
     margin-top: 0;
     padding-top: 0;
     border-top: none;
 }

 .serviceBox-section-title {
     font-weight: bold;
     padding-bottom: 8px;
     border-bottom: 1px dashed #D9D9D9;
 }

 .serviceBox-time {
     display: flex;
     flex-direction: column;
     gap: 4px;
     padding: 10px 0;
 }



 /* 动画效果 */
 .serviceBox {
     opacity: 0;
     transition: all 0.2s ease-in-out;
 }

 .service:hover .serviceBox,
 .serviceBox:hover {
     display: block;
     opacity: 1;
 }

 /* 添加透明过渡区域，改善hover体验 */
 .service::before {
     content: '';
     position: absolute;
     top: 0;
     right: 30px;
     /* 覆盖service和serviceBox之间的间隙 */
     width: 50px;
     /* 透明区域宽度 */
     height: 100%;
     background: transparent;
     z-index: 999;
     pointer-events: none;
     /* 不阻止点击事件 */
 }

 .service:hover::before {
     pointer-events: auto;
     /* hover时启用指针事件 */
 }

 /* 添加指向侧边栏的小箭头 */
 .serviceBox::after {
     content: '';
     position: absolute;
     top: 50px;
     right: -8px;
     transform: translateY(-50%);
     width: 0;
     height: 0;
     border-left: 8px solid #fff;
     border-top: 8px solid transparent;
     border-bottom: 8px solid transparent;
 }

 .serviceBox::before {
     content: '';
     position: absolute;
     top: 50px;
     right: -9px;
     transform: translateY(-50%);
     width: 0;
     height: 0;
     border-left: 9px solid #ddd;
     border-top: 9px solid transparent;
     border-bottom: 9px solid transparent;
 }

 .bias {
     margin-left: 2px;
 }

 .loveImg {
     width: 12vw;
     height: 12vw;
     min-width: 156px;
     min-height: 156px;
     object-fit: contain;

 }

 .imgBox {
     width: 12vw;
     height: 12vw;
     min-width: 156px;
     min-height: 156px;
     position: relative;
 }

 .imgBox::after {
     width: 100%;
     height: 100%;
     content: "";
     position: absolute;
     top: 0;
     left: 0;
     background-color: rgba(0, 0, 0, .1);
 }