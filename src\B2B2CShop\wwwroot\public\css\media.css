/* 媒体查询 -公共*/
@media screen and (max-width: 1300px) {
  .main {
    margin-left: 10% !important;
    width: 80% !important;
    font-size: 15px !important;
  }

  /* .content{
     margin-left: 10% !important;
     width: 80% !important;
   } */
  /* .breadBox>div:first-child{
     margin-left: 50px !important;
   } */
  /* 产品详情 -服务声明 */
  .serverInfo {
    font-size: 11px;
  }

  ._main {
    margin-left: 10% !important;
    width: 65% !important;
  }

  .icon-icon1 {
    top: 60px;
  }

  .mainBox2_content {
    min-height: 200px !important;
  }

  .mainBox2_content_home {
    min-height: 180px !important;
    width: calc((100% - 24px) / 4) !important;
  }

  .mainBox2_content_home:not(:last-child) {
    margin-right: 8px !important;
  }

  .mainBox2_content_desc {
    /* font-size: 10px !important; */
  }

  .mainBox2_content>div:nth-child(3)>.icon-star {
    font-size: 12px;
    color: var(--warning);
  }

  .mainBox2_content_price {
    margin-top: 2px;
    font-size: 14px;
  }

  .orderAside {
    right: 1% !important;
  }

  .asideTextTitle {
    font-size: 13px !important;
  }

  .tableBox {
    font-size: 12px !important;
  }

  .aside_btnBox2>div {
    font-size: 10px !important;
  }

  .aside_btnBox2>div>i {
    font-size: 12px !important;
  }

  /* 产品详情-结束 */


  /* 确认订单 */
  .checkOrderImage {
    margin-left: 5vw !important;
  }

  .stepBoxContainer {
    margin-right: 5vw !important;
  }

  .checkOrderMain {
    width: 90% !important;
    margin-left: 5% !important;
  }

  /* 确认订单 -结束 */


  /* 公共样式 */
  .acountBox {
    margin-left: 0% !important;
  }

  .computer {
    margin-left: 5px;
    font-size: 12px !important;
    margin: 5px 0px 2px 3px;
  }

  .computer>.icon-jianhao {
    font-size: 12px;
  }

  .computer>.icon-tianjia1 {
    font-size: 12px;
  }

  .button {
    padding: .8vw .5vw !important;
    font-size: 1.1vw !important;
  }

  .layuiDateBox {
    margin-top: 10px;
  }
}